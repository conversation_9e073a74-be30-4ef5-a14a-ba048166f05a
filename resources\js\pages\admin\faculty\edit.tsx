import InputError from '@/components/input-error';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { ChangeEvent, FormEventHandler, useState } from 'react';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Content } from '@tiptap/react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';


const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Edit Faculty',
        href: '/faculty/edit',
    },
];

type PageProps = {
    draftToken: string;
};

interface FacultyData {
    id: number;
    name: string; 
    slug: string;
    designation: string;
    department: string;
    image: string;
    bio: string;
    about: string;
    education: string;
    research: string;
    interests: string;
    official_email: string;
    secondary_email: string;
    primary_phone: string;
    secondary_phone: string;
    active_status: 'active' | 'inactive';
    
}


export default function FacultyEdit({ currentFaculty }: { currentFaculty: FacultyData }) {
    const [name, setName] = useState(currentFaculty.name);
    const [designation, setDesignation] = useState(currentFaculty.designation);
    const [department, setDepartment] = useState(currentFaculty.department);
    const [bio, setBio] = useState(currentFaculty.bio || '');
    const [about, setAbout] = useState(currentFaculty.about || '');
    const [education, setEducation] = useState(currentFaculty.education || '');
    const [research, setResearch] = useState(currentFaculty.research || '');
    const [interests, setInterests] = useState(currentFaculty.interests || '');
    const [officialEmail, setOfficialEmail] = useState(currentFaculty.official_email);
    const [secondaryEmail, setSecondaryEmail] = useState(currentFaculty.secondary_email || '');
    const [primaryPhone, setPrimaryPhone] = useState(currentFaculty.primary_phone);
    const [secondaryPhone, setSecondaryPhone] = useState(currentFaculty.secondary_phone || '');
    const [image, setImage] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string | null>(currentFaculty.image);
    const [activeStatus, setActiveStatus] = useState(currentFaculty.active_status);
    const { errors, draftToken } = usePage<PageProps>().props;
    //const [value, setValue] = useState<Content>(null);
    const [aboutValue, setAboutValue] = useState<Content>(null);
    const [educationValue, setEducationValue] = useState<Content>(null);
    const [researchValue, setResearchValue] = useState<Content>(null);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        router.post(route('admin.faculty.update', currentFaculty.id), {
            _method: 'put',
            name,
            designation,
            department,
            bio,
            about,
            education,
            research,
            interests,
            official_email: officialEmail,
            secondary_email: secondaryEmail,
            primary_phone: primaryPhone,
            secondary_phone: secondaryPhone,
            image,
            active_status: activeStatus,
            draft_token: draftToken,
        });
    };

    const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setImage(file);
            setImagePreview(URL.createObjectURL(file));
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Edit Faculty" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <h1 className="text-2xl font-bold">Edit Faculty Member</h1>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={submit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="name">Name *</Label>
                                        <Input
                                            type="text"
                                            id="name"
                                            placeholder="Full Name"
                                            value={name}
                                            onChange={(e) => setName(e.target.value)}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="designation">Designation *</Label>
                                        <Input
                                            type="text"
                                            id="designation"
                                            placeholder="Professor, Associate Professor, etc."
                                            value={designation}
                                            onChange={(e) => setDesignation(e.target.value)}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="department">Department *</Label>
                                        <Input
                                            type="text"
                                            id="department"
                                            placeholder="Department Name"
                                            value={department}
                                            onChange={(e) => setDepartment(e.target.value)}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="active_status">Active Status</Label>
                                        <Select value={activeStatus} onValueChange={(value: 'active' | 'inactive') => setActiveStatus(value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="official_email">Official Email *</Label>
                                        <Input
                                            type="email"
                                            id="official_email"
                                            placeholder="<EMAIL>"
                                            value={officialEmail}
                                            onChange={(e) => setOfficialEmail(e.target.value)}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="secondary_email">Secondary Email</Label>
                                        <Input
                                            type="email"
                                            id="secondary_email"
                                            placeholder="<EMAIL>"
                                            value={secondaryEmail}
                                            onChange={(e) => setSecondaryEmail(e.target.value)}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="primary_phone">Primary Phone *</Label>
                                        <Input
                                            type="text"
                                            id="primary_phone"
                                            placeholder="+1234567890"
                                            value={primaryPhone}
                                            onChange={(e) => setPrimaryPhone(e.target.value)}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="secondary_phone">Secondary Phone</Label>
                                        <Input
                                            type="text"
                                            id="secondary_phone"
                                            placeholder="+1234567890"
                                            value={secondaryPhone}
                                            onChange={(e) => setSecondaryPhone(e.target.value)}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="image">Profile Image</Label>
                                        <Input
                                            type="file"
                                            id="image"
                                            accept="image/*"
                                            onChange={handleFileChange}
                                        />
                                        {imagePreview && (
                                            <div className="mt-2">
                                                <img src={imagePreview} alt="Preview" className="w-20 h-20 object-cover rounded" />
                                            </div>
                                        )}
                                    </div>
                                </div>

                                <div className="mt-4 grid grid-cols-1 gap-4">
                                    <div>
                                        <Label htmlFor="bio">Bio</Label>
                                        <Textarea
                                            id="bio"
                                            placeholder="Brief biography"
                                            value={bio}
                                            onChange={(e) => setBio(e.target.value)}
                                        />
                                    </div>
                                    

                                    {/*<div>
                                        <Label htmlFor="about">About</Label>
                                        <Textarea
                                            id="about"
                                            placeholder="About the faculty member"
                                            value={about}
                                            onChange={(e) => setAbout(e.target.value)}
                                        />
                                    </div>*/}

                                    <div className="grid gap-2 overflow-hidden">    
                                        <Label htmlFor="about">About</Label>                                    
                                        <MinimalTiptapEditor
                                            value={currentFaculty.about}
                                            onChange={(val) => {
                                                setAboutValue(val as string);
                                                setAbout(val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken as string,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                        <div className="grid gap-2">
                                        Preview:
                                    </div>
                                        <div className="richtext-output">
                                            <div dangerouslySetInnerHTML={{ __html: (aboutValue as string) || '' }} />
                                        </div>
                                        <InputError message={errors.about} />
                                        <hr />
                                    </div>

                                    {/*<div>
                                        <Label htmlFor="education">Education</Label>
                                        <Textarea
                                            id="education"
                                            placeholder="Educational background"
                                            value={education}
                                            onChange={(e) => setEducation(e.target.value)}
                                        />
                                    </div>*/}

                                     <div className="grid gap-2 overflow-hidden">    
                                        <Label htmlFor="education">Education</Label>
                                        <MinimalTiptapEditor
                                            value={currentFaculty.education}
                                            onChange={(val) => {
                                                setEducationValue(val as string);
                                                setEducation(val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken as string,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                        <div className="grid gap-2">
                                        Preview:
                                    </div>
                                        <div className="richtext-output">
                                            <div dangerouslySetInnerHTML={{ __html: (educationValue as string) || '' }} />
                                        </div>
                                        <InputError message={errors.education} />
                                        <hr />
                                    </div>

                                    {/*<div>
                                        <Label htmlFor="research">Research</Label>
                                        <Textarea
                                            id="research"
                                            placeholder="Research interests and work"
                                            value={research}
                                            onChange={(e) => setResearch(e.target.value)}
                                        />
                                    </div>*/}

                                     <div className="grid gap-2 overflow-hidden">    
                                        <Label htmlFor="research">Research </Label>                                    
                                        <MinimalTiptapEditor
                                            value={currentFaculty.research}
                                            onChange={(val) => {
                                                setResearchValue(val as string);
                                                setResearch(val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken as string,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                        <div className="grid gap-2">
                                        Preview:
                                    </div>
                                        <div className="richtext-output">
                                            <div dangerouslySetInnerHTML={{ __html: (researchValue as string) || '' }} />
                                        </div>
                                        <InputError message={errors.research} />
                                        <hr />
                                    </div>

                                    <div>
                                        <Label htmlFor="interests">Interests</Label>
                                        <Textarea
                                            id="interests"
                                            placeholder="Academic and personal interests"
                                            value={interests}
                                            onChange={(e) => setInterests(e.target.value)}
                                        />
                                    </div>
                                </div>

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit">
                                        <span>Update Faculty Member</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
