import InputError from '@/components/input-error';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { cn } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { Content } from '@tiptap/react';
import { format } from 'date-fns';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { ChangeEvent, FormEventHandler, useEffect, useState } from 'react';
import '../text-editor/RichTextStyles.css';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Create Notice',
        href: route('admin.noticeboard.create'),
    },
];

type NoticeForm = {
    title: string;
    category: string;
    content: string;
    image: File | null;
    attachment: File | null;
    published_at: string;
    draft_token: string | null;
};

type PageProps = {
    draftToken: string;
};

const noticeCategories = [
    { label: 'All Department', value: 'all' },
    { label: 'CSE', value: 'cse' },
    { label: 'BBA', value: 'bba' },
    { label: 'BMB', value: 'bmb' },
    { label: 'ECE', value: 'ece' },
    { label: 'Academic', value: 'academic' },
    { label: 'Official', value: 'official' },
];

export default function NoticeboardCreate() {
    const { draftToken } = usePage<PageProps>().props;
    const [value, setValue] = useState<Content>(null);
    const [publishedDate, setPublishedDate] = useState<Date | undefined>(new Date()); // Set the initial date to today
    console.log('draftToken: ', draftToken);

    const { data, setData, post, processing, errors } = useForm<NoticeForm>({
        title: '',
        category: '',
        content: '',
        image: null,
        attachment: null,
        published_at: '',
        draft_token: draftToken || null,
    });

    useEffect(() => {
        setData('published_at', format(new Date(), 'yyyy-MM-dd'));
    }, []);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        // Set published_at if date is selected

        console.log('notice data before  save:', data);

        post(route('admin.noticeboard.store'));
    };

    const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('image', file);
        }
    };

    const handleAttachmentChange = (e: ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('attachment', file);
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Notice" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Create Notice</div>
                        <Button>
                            <Link href={route('admin.noticeboard.index')} prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form className="space-y-6" onSubmit={submit}>
                                <div className="grid gap-6">
                                    <div className="grid gap-2">
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            id="title"
                                            type="text"
                                            value={data.title}
                                            onChange={(e) => setData('title', e.target.value)}
                                            placeholder="Enter notice title"
                                        />
                                        <InputError message={errors.title} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="category">Category *</Label>
                                        <Select value={data.category} onValueChange={(value) => setData('category', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select category" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {noticeCategories.map((option) => (
                                                    <SelectItem key={option.value} value={option.value}>
                                                        {option.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        <InputError message={errors.category} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="image">Image *</Label>
                                        <Input id="image" type="file" accept="image/*" onChange={handleImageChange} />
                                        {data.image && (
                                            <img src={URL.createObjectURL(data.image)} alt="Preview" className="h-20 w-20 rounded object-cover" />
                                        )}
                                        <InputError message={errors.image} />
                                    </div>
                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="content">Content *</Label>
                                        <MinimalTiptapEditor
                                            value={value}
                                            onChange={(val) => {
                                                setValue(val as string);
                                                setData('content', val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="min-h-[200px]"
                                            placeholder="Enter notice content..."
                                        />
                                        <InputError message={errors.content} />
                                    </div>
                                    <div className="grid gap-2">
                                        <Label htmlFor="content-preview">Notice Preview:</Label>
                                        <div className="richtext-output">
                                            <div dangerouslySetInnerHTML={{ __html: (value as string) || '' }} />
                                        </div>
                                    </div>
                                    <hr />

                                    <div className="grid gap-2">
                                        <Label htmlFor="published_at">Published Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        'w-full justify-start text-left font-normal',
                                                        !publishedDate && 'text-muted-foreground',
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {publishedDate ? format(publishedDate, 'PPP') : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={publishedDate}
                                                    onSelect={(date) => {
                                                        setPublishedDate(date);
                                                        setData('published_at', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.published_at} />
                                    </div>

                                    <div className="grid gap-2">
                                        <Label htmlFor="attachment">Attachment (PDF or Image)</Label>
                                        <Input id="attachment" type="file" accept=".pdf,.jpg,.jpeg,.png,.gif" onChange={handleAttachmentChange} />
                                        <InputError message={errors.attachment} />
                                    </div>
                                    <div className="flex justify-end space-x-2">
                                        <Button type="button" variant="outline">
                                            <Link href={route('admin.noticeboard.index')} prefetch>
                                                Cancel
                                            </Link>
                                        </Button>
                                        <Button type="submit" disabled={processing}>
                                            {processing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                                            Create Notice
                                        </Button>
                                    </div>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
