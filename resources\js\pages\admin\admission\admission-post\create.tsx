import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Content } from '@tiptap/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admission Management',
        href: '/admin/admission',
    },
    {
        title: 'Add Admission Post',
        href: '/admin/admission/posts/create',
    },
];

type PageProps = {
    draftToken: string;
};

export default function AdmissionPostCreate() {
    const [publishedDate, setPublishedDate] = useState<Date>();
    const { draftToken } = usePage<PageProps>().props;
    const [value, setValue] = useState<Content>(null);

    const { data, setData, post, errors, processing } = useForm<{
        title: string;
        admission_circular_content: string;
        published_date: string;
        draft_token: string;
    }>({
        title: '',
        admission_circular_content: '',
        published_date: '',
        draft_token: draftToken,
    });

    function handleFormSubmit(e: React.FormEvent<HTMLFormElement>) {
        e.preventDefault();
        console.log('data before  save:', data);
        post(route('admin.admission.posts.store'));

    }

   

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Admission Post" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="text-xl text-slate-600">Create Admission Post</div>

                        <Button>
                            <Link href="/admin/admission?tab=admission_post" prefetch>
                                Go Back
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={handleFormSubmit}>
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="title">Title *</Label>
                                        <Input
                                            type="text"
                                            id="title"
                                            placeholder="Enter post title"
                                            value={data.title}
                                            onChange={(e) => setData('title', e.target.value)}
                                            aria-invalid={!!errors.title}
                                        />
                                        <InputError message={errors.title} />
                                    </div>

                                    {/*<div>
                                        <Label htmlFor="admission_circular_content">Admission Circular Content</Label>
                                        <Textarea
                                            id="admission_circular_content"
                                            placeholder="Enter circular content"
                                            value={data.admission_circular_content}
                                            onChange={(e) => setData('admission_circular_content', e.target.value)}
                                            aria-invalid={!!errors.admission_circular_content}
                                            rows={5}
                                        />
                                        <InputError message={errors.admission_circular_content} />
                                    </div>*/}

                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="admission_circular_content">Admission Circular Content *</Label>
                                        <MinimalTiptapEditor
                                            value={value}
                                            onChange={(val) => {
                                                setValue(val as string);
                                                setData('admission_circular_content', val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                    </div>
                                    <div className="richtext-output">
                                        <div dangerouslySetInnerHTML={{ __html: (value as string) || '' }} />
                                    </div>
                                    <InputError message={errors.admission_circular_content} />
                                    <hr />

                                    <div>
                                        <Label htmlFor="published_date">Published Date *</Label>
                                        <Popover>
                                            <PopoverTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    className={cn(
                                                        'w-full justify-start text-left font-normal',
                                                        !publishedDate && 'text-muted-foreground',
                                                    )}
                                                >
                                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                                    {publishedDate ? format(publishedDate, 'PPP') : <span>Pick a date</span>}
                                                </Button>
                                            </PopoverTrigger>
                                            <PopoverContent className="w-auto p-0">
                                                <Calendar
                                                    mode="single"
                                                    selected={publishedDate}
                                                    onSelect={(date) => {
                                                        setPublishedDate(date);
                                                        setData('published_date', date ? format(date, 'yyyy-MM-dd') : '');
                                                    }}
                                                    initialFocus
                                                />
                                            </PopoverContent>
                                        </Popover>
                                        <InputError message={errors.published_date} />
                                    </div>
                                </div>

                                <div className="mt-6 text-end">
                                    <Button size={'lg'} type="submit" disabled={processing}>
                                        {processing && <Loader2 className="animate-spin" />}
                                        <span>Create Admission Post</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
