import { Head } from '@inertiajs/react';
import React from 'react';
import { FaCalendar<PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaUser } from 'react-icons/fa';
import '../text-editor/RichTextStyles.css';

// Assuming 'Back' component is in a relative path and styled with Tailwind
import Back from '../common/back/Back';

// 1. TYPESCRIPT INTERFACE (can be in a central types file, e.g., @/types)
// ---
export interface Noticeboard {
    id: number;
    title: string;
    slug: string;
    category: string;
    content: string;
    image: string | null;
    attachment: string | null;
    created_at: string;
    published_at: string;
    active_status: string;
}

// Props interface for type-checking the data from Inertia
interface Props {
    notice: Noticeboard;
}

// 2. MAIN COMPONENT
// ---
const NoticeView: React.FC<Props> = ({ noticeDataItem }) => {
    // Handle case where notice might not be found (though Inertia usually handles this with a 404)
    console.log(noticeDataItem);
    if (!noticeDataItem) {
        return (
            <div className="flex h-screen items-center justify-center">
                <h2 className="text-2xl font-bold text-gray-700">Notice Not Found</h2>
            </div>
        );
    }

    // Format date for better readability
    const formattedDate = new Date(noticeDataItem.created_at).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    });

    return (
        <>
            {/* Set the page title dynamically */}
            <Head title={noticeDataItem.title} />

            <Back title="Notice Details" />

            {/* Main container for the post */}
            <article className="mx-auto my-10 max-w-4xl rounded-xl bg-white p-6 shadow-lg sm:p-8">
                {/* Post Header */}
                <header className="mb-6 border-b border-gray-200 pb-6">
                    <h1 className="text-3xl leading-tight font-extrabold text-gray-900 md:text-4xl">{noticeDataItem.title}</h1>
                    {/* Post Metadata */}
                    <div className="mt-4 flex flex-col gap-x-6 gap-y-2 text-sm text-gray-500 sm:flex-row sm:items-center">
                        <div className="flex items-center gap-2">
                            <FaCalendarAlt className="text-gray-400" />
                            <span>Published on: {formattedDate}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <FaFolder className="text-gray-400" />
                            <span>Category: {noticeDataItem.category}</span>
                        </div>
                        <div className="flex items-center gap-2">
                            <FaUser className="text-gray-400" />
                            <span>Posted by: Admin</span>
                        </div>
                    </div>
                </header>

                {/* Post Body */}
                <div className="post-content">
                    {/* Conditionally render the cover image */}
                    {noticeDataItem.image && (
                        <img src={noticeDataItem.image} alt={noticeDataItem.title} className="mb-6 h-auto w-full rounded-lg object-cover shadow-md" />
                    )}

                    {/* Render HTML content safely using Tailwind's Typography plugin */}
                    <div className="richtext-output">
                        <div dangerouslySetInnerHTML={{ __html: noticeDataItem.content }} />
                    </div>
                </div>

                {/* Attachment Section - renders only if an attachment exists */}
                {noticeDataItem.attachment && (
                    <aside className="mt-10 border-t border-gray-200 pt-6">
                        <h2 className="mb-4 text-2xl font-semibold text-gray-800">Attachment</h2>

                        {/* PDF Viewer */}
                        <div className="h-[80vh] w-full overflow-hidden rounded-md border border-gray-300 shadow-inner">
                            <iframe
                                src={noticeDataItem.attachment}
                                title={`${noticeDataItem.title} - Attachment`}
                                className="h-full w-full border-none"
                            />
                        </div>

                        {/* Download Button */}
                        <a
                            href={noticeDataItem.attachment}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="mt-6 inline-flex transform items-center gap-2 rounded-lg bg-blue-600 px-6 py-3 font-semibold text-white shadow-md transition-transform hover:scale-105 hover:bg-blue-700"
                        >
                            Download Attachment
                        </a>
                    </aside>
                )}
            </article>
        </>
    );
};

export default NoticeView;
