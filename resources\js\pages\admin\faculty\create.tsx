import InputError from '@/components/input-error';
import { MinimalTiptapEditor } from '@/components/minimal-tiptap';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm, usePage } from '@inertiajs/react';
import { Content } from '@tiptap/react';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Create Faculty',
        href: '/faculty/create',
    },
];

type PageProps = {
    draftToken: string;
};

export default function FacultyCreate() {
    const { draftToken } = usePage<PageProps>().props;
    const [value, setValue] = useState<Content>(null);
     const [aboutValue, setAboutValue] = useState<Content>(null);
    const [educationValue, setEducationValue] = useState<Content>(null);
    const [researchValue, setResearchValue] = useState<Content>(null);
    console.log('draftToken: ', draftToken);
    const { data, setData, post, errors, processing } = useForm<{
        name: string;
        designation: string;
        department: string;
        image: File | null;
        bio: string;
        about: string;
        education: string;
        research: string;
        interests: string;
        official_email: string;
        secondary_email: string;
        primary_phone: string;
        secondary_phone: string;
        active_status: 'active' | 'inactive';
        draft_token: string;
    }>({
        name: '',
        designation: '',
        department: '',
        image: null,
        bio: '',
        about: '',
        education: '',
        research: '',
        interests: '',
        official_email: '',
        secondary_email: '',
        primary_phone: '',
        secondary_phone: '',
        active_status: 'active',
        draft_token: draftToken,
    });

    function handleFormSubmit(e: React.FormEvent<HTMLFormElement>) {
        console.log('faculty data before saving: ', data);    
        e.preventDefault();
        post(route('admin.faculty.store'));
    }

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Create Faculty" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <h1 className="text-2xl font-bold">Create Faculty Member</h1>
                    </div>

                    <Card>
                        <CardContent>
                            <form onSubmit={handleFormSubmit}>
                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <Label htmlFor="name">Name *</Label>
                                        <Input
                                            type="text"
                                            id="name"
                                            placeholder="Full Name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            aria-invalid={!!errors.name}
                                        />
                                        <InputError message={errors.name} />
                                    </div>

                                    <div>
                                        <Label htmlFor="designation">Designation *</Label>
                                        <Input
                                            type="text"
                                            id="designation"
                                            placeholder="Professor, Associate Professor, etc."
                                            value={data.designation}
                                            onChange={(e) => setData('designation', e.target.value)}
                                            aria-invalid={!!errors.designation}
                                        />
                                        <InputError message={errors.designation} />
                                    </div>

                                    <div>
                                        <Label htmlFor="department">Department *</Label>
                                        <Input
                                            type="text"
                                            id="department"
                                            placeholder="Department Name"
                                            value={data.department}
                                            onChange={(e) => setData('department', e.target.value)}
                                            aria-invalid={!!errors.department}
                                        />
                                        <InputError message={errors.department} />
                                    </div>
                                     <div>
                                        <Label htmlFor="active_status">Active Status</Label>
                                        <Select value={data.active_status} onValueChange={(value: 'active' | 'inactive') => setData('active_status', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="active">Active</SelectItem>
                                                <SelectItem value="inactive">Inactive</SelectItem>
                                            </SelectContent>
                                            <InputError message={errors.active_status} />
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="official_email">Official Email *</Label>
                                        <Input
                                            type="email"
                                            id="official_email"
                                            placeholder="<EMAIL>"
                                            value={data.official_email}
                                            onChange={(e) => setData('official_email', e.target.value)}
                                            aria-invalid={!!errors.official_email}
                                        />
                                        <InputError message={errors.official_email} />
                                    </div>

                                    <div>
                                        <Label htmlFor="secondary_email">Secondary Email</Label>
                                        <Input
                                            type="email"
                                            id="secondary_email"
                                            placeholder="<EMAIL>"
                                            value={data.secondary_email}
                                            onChange={(e) => setData('secondary_email', e.target.value)}
                                            aria-invalid={!!errors.secondary_email}
                                        />
                                        <InputError message={errors.secondary_email} />
                                    </div>

                                    <div>
                                        <Label htmlFor="primary_phone">Primary Phone *</Label>
                                        <Input
                                            type="text"
                                            id="primary_phone"
                                            placeholder="+1234567890"
                                            value={data.primary_phone}
                                            onChange={(e) => setData('primary_phone', e.target.value)}
                                            aria-invalid={!!errors.primary_phone}
                                        />
                                        <InputError message={errors.primary_phone} />
                                    </div>

                                    <div>
                                        <Label htmlFor="secondary_phone">Secondary Phone</Label>
                                        <Input
                                            type="text"
                                            id="secondary_phone"
                                            placeholder="+1234567890"
                                            value={data.secondary_phone}
                                            onChange={(e) => setData('secondary_phone', e.target.value)}
                                            aria-invalid={!!errors.secondary_phone}
                                        />
                                        <InputError message={errors.secondary_phone} />
                                    </div>

                                    <div>
                                        <Label htmlFor="image">Profile Image</Label>
                                        <Input
                                            type="file"
                                            id="image"
                                            accept="image/*"
                                            onChange={(e) => setData('image', e.target.files?.[0] || null)}
                                            aria-invalid={!!errors.image}
                                        />
                                        <InputError message={errors.image} />
                                    </div>
                                </div>

                                <div className="mt-4 grid grid-cols-1 gap-4">
                                    <div>
                                        <Label htmlFor="bio">Bio</Label>
                                        <Textarea
                                            id="bio"
                                            placeholder="Brief biography"
                                            value={data.bio}
                                            onChange={(e) => setData('bio', e.target.value)}
                                            aria-invalid={!!errors.bio}
                                        />
                                        <InputError message={errors.bio} />
                                    </div>
                                   

                                    {/*} <div>
                                        <Label htmlFor="about">About</Label>
                                        <Textarea
                                            id="about"
                                            placeholder="About the faculty member"
                                            value={data.about}
                                            onChange={(e) => setData('about', e.target.value)}
                                            aria-invalid={!!errors.about}
                                        />
                                        <InputError message={errors.about} />
                                    </div>*/}

                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="about">About *</Label>
                                        <MinimalTiptapEditor
                                            value={aboutValue}
                                            onChange={(val) => {
                                                setAboutValue(val as string);
                                                setData('about', val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                    </div>
                                    <div className="grid gap-2">
                                        Preview:
                                    </div>
                                    <div className="richtext-output">
                                        <div dangerouslySetInnerHTML={{ __html: (aboutValue as string) || '' }} />
                                    </div>
                                    <InputError message={errors.about} />
                                    <hr />

                                    {/*<div>
                                        <Label htmlFor="education">Education</Label>
                                        <Textarea
                                            id="education"
                                            placeholder="Educational background"
                                            value={data.education}
                                            onChange={(e) => setData('education', e.target.value)}
                                            aria-invalid={!!errors.education}
                                        />
                                        <InputError message={errors.education} />
                                    </div>*/}

                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="education">Education</Label>
                                        <MinimalTiptapEditor
                                            value={educationValue}
                                            onChange={(val) => {
                                                setEducationValue(val as string);
                                                setData('education', val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                    </div>
                                    <div className="grid gap-2">
                                        Preview:
                                    </div>
                                    <div className="richtext-output">
                                        <div dangerouslySetInnerHTML={{ __html: (educationValue as string) || '' }} />
                                    </div>
                                    <InputError message={errors.education} />
                                    <hr />

                                    {/*<div>
                                        <Label htmlFor="research">Research</Label>
                                        <Textarea
                                            id="research"
                                            placeholder="Research interests and work"
                                            value={data.research}
                                            onChange={(e) => setData('research', e.target.value)}
                                            aria-invalid={!!errors.research}
                                        />
                                        <InputError message={errors.research} />
                                    </div>*/}
                                    <div className="grid gap-2 overflow-hidden">
                                        <Label htmlFor="research">Research</Label>
                                        <MinimalTiptapEditor
                                            value={researchValue}
                                            onChange={(val) => {
                                                setResearchValue(val as string);
                                                setData('research', val as string); // sync editor output to form
                                            }}
                                            uploadConfig={{
                                                draftToken: draftToken,
                                                uploadUrl: route('admin.editor.uploads.store'),
                                                deleteUrl: (id: number | string) => route('admin.editor.uploads.destroy', id),
                                            }}
                                            className="grid max-w-full gap-2 overflow-hidden"
                                            editorContentClassName="p-5 max-w-full overflow-hidden"
                                            output="html"
                                            placeholder="Enter your description..."
                                            autofocus={true}
                                            editable={true}
                                            editorClassName="focus:outline-hidden max-w-full overflow-hidden"
                                        />
                                    </div>
                                    <div className="grid gap-2">
                                        Preview:
                                    </div>
                                    <div className="richtext-output">
                                        <div dangerouslySetInnerHTML={{ __html: (researchValue as string) || '' }} />
                                    </div>
                                        <InputError message={errors.research} />
                                    <hr />

                                    <div>
                                        <Label htmlFor="interests">Interests</Label>
                                        <Textarea
                                            id="interests"
                                            placeholder="Academic and personal interests"
                                            value={data.interests}
                                            onChange={(e) => setData('interests', e.target.value)}
                                            aria-invalid={!!errors.interests}
                                        />
                                        <InputError message={errors.interests} />
                                    </div>
                                </div>

                                <div className="mt-4 text-end">
                                    <Button size={'lg'} type="submit" disabled={processing}>
                                        {processing && <Loader2 className="animate-spin" />}
                                        <span>Create Faculty Member</span>
                                    </Button>
                                </div>
                            </form>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
