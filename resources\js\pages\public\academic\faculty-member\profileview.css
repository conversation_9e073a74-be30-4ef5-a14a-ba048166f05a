.faculty-profile-container {
    max-width: 900px;
    margin: 50px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  }
  
  /* Top Section: Profile Image and Details */
  .profile-header {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    align-items: center;
  }
  
  .profile-image img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .profile-details h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #333;
  }
  
  .profile-details p {
    font-size: 1rem;
    color: #555;
    line-height: 1.5;
    
  }
  
  .contacts p {
    margin: 5px 0;
    font-size: 1rem;
    color: #333;
  }
  
  /* Tabbed Pane Section */
  .profile-tab-section {
    width: 900px;
    margin: 50px auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #ddd;
  }
  
  /* Tab Buttons - Horizontal Alignment for Larger Screens */
  .tabs {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }
  
  .tabs button {
   
    background-color: #f0f0f0;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    text-align: center;
    font-size: 1rem;
    color: #333;
  }
  
  .tabs button.active {
    background-color: #007bff;
    color: white;
  }
  
  .tabs button:hover {
    background-color: #0056b3;
    color: white;
  }
  
  /* Tab Content */
  .tab-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
  }
  
  .tab-content p,
  .tab-content ul {
    font-size: 1rem;
    color: #555;
    line-height: 1.5;
  }
  
  .tab-content ul {
    list-style-type: disc;
    margin-left: 20px;
  }

.content-justify{
  text-align: justify;
}
  
  
  
  /* Mobile Responsiveness */
  @media (max-width: 768px) {
    .faculty-profile-container{
        max-width: 92%;
    }
    .profile-header {
      flex-direction: column;
      align-items: center;
    }

    .profile-tab-section{
        width: 400px;
      
    }
    .profile-details {
      text-align: left;
    }

    .profile-details h2 {
        text-align: center;
    }
  
      .tabs {
        display: block;
      }

      .tabs button{
        font-size: 14px;
        margin: 10px 10px 0 0;
      }

      .tab-content h3 {
        font-size: 1.3rem;
        margin-bottom: 10px;
        color: #333;
    }

  }

  @media (max-width: 480px) {
    .tabs {
      display: block;
    }

    .tabs button{
      font-size: 12px;
      margin: 8px 8px 0 0;
    }

  }
  