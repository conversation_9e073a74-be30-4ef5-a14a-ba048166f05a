import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import { Mail, Phone } from 'lucide-react';
import Back from '../../common/back/Back';
import '@/components/minimal-tiptap/styles/index.css';

// Define the TypeScript interface for a faculty member from database
interface FacultyMember {
  id: number;
  name: string;
  slug: string;
  designation: string;
  department: string;
  image: string | null;
  bio: string;
  about: string;
  education: string;
  research: string;
  interests: string;
  official_email: string;
  secondary_email: string | null;
  primary_phone: string;
  secondary_phone: string | null;
  active_status: string;
  created_at: string;
  updated_at: string;
}

interface Props {
    facultyMember: FacultyMember;
}



const FacultyProfilePage: React.FC<Props> = ({ facultyMember }) => {
  const [activeTab, setActiveTab] = useState('about');

  // Data for the tabs to make the component cleaner
  const tabs = [
    { id: 'about', label: 'About' },
    { id: 'education', label: 'Education' },
    { id: 'research', label: 'Research' },
    { id: 'interests', label: 'Research Interests' },
  ];

  if (!facultyMember) {
    return <h2>Faculty member not found</h2>;
  }

  return (
    <>
      <Head title={facultyMember.name} />
      <Back title="Faculty Profile" />
      
      <div className="container mx-auto max-w-4xl px-4 py-12 ">
        {/* Top Section: Profile Image and Details */}
        <div className="rounded-lg mb-8 bg-white p-6 shadow-lg inset-shadow-sm border-1 border-green-500 border-dashed">
          <div className="flex flex-col items-center gap-6 md:flex-row md:items-start">
            <img
              src={facultyMember.image || '/images/default-avatar.png'}
              alt={facultyMember.name}
              className="h-40 w-40 shrink-0 rounded-full object-cover shadow-md"
            />
            <div className="text-center md:text-left">
              <h2 className="text-3xl font-bold text-gray-800">{facultyMember.name}</h2>
              <p className="mt-1 text-xl font-medium text-gray-600">{facultyMember.designation}</p>
              <p className="mt-1 text-md text-gray-500">{facultyMember.department}</p>
              <p className="mt-4 text-justify leading-relaxed text-gray-700">{facultyMember.bio}</p>
              <div className="mt-4 flex flex-wrap justify-center gap-x-6 gap-y-2 md:justify-start">
                {facultyMember.official_email && (
                  <a href={`mailto:${facultyMember.official_email}`} className="flex items-center gap-2 text-gray-700 hover:text-blue-600">
                    <Mail size={16} /> {facultyMember.official_email}
                  </a>
                )}
                {facultyMember.secondary_email && (
                  <a href={`mailto:${facultyMember.secondary_email}`} className="flex items-center gap-2 text-gray-700 hover:text-blue-600">
                    <Mail size={16} /> {facultyMember.secondary_email}
                  </a>
                )}
                {facultyMember.primary_phone && (
                  <a href={`tel:${facultyMember.primary_phone}`} className="flex items-center gap-2 text-gray-700 hover:text-blue-600">
                    <Phone size={16} /> {facultyMember.primary_phone}
                  </a>
                )}
                {facultyMember.secondary_phone && (
                  <a href={`tel:${facultyMember.secondary_phone}`} className="flex items-center gap-2 text-gray-700 hover:text-blue-600">
                    <Phone size={16} /> {facultyMember.secondary_phone}
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Tabbed Pane Section */}
        <div className="rounded-lg bg-white p-6 shadow-lg inset-shadow-sm border-1 border-green-500 border-dashed">
          <div className="mb-6 flex flex-wrap justify-center gap-2 border-b border-gray-200 pb-4">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-green-700 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="prose max-w-none">
            {activeTab === 'about' && (
              <div>
                <h2 className='text-2xl font-bold text-[#333] mb-2.5'>About</h2>
                <div
                  className="ProseMirror"
                  dangerouslySetInnerHTML={{ __html: facultyMember.about || '<p>No information available.</p>' }}
                />
              </div>
            )}
            {activeTab === 'education' && (
              <div>
                <h2 className='text-2xl font-bold text-[#333] mb-2.5'>Education</h2>
                <div
                  className="ProseMirror"
                  dangerouslySetInnerHTML={{ __html: facultyMember.education || '<p>No education information available.</p>' }}
                />
              </div>
            )}
            {activeTab === 'research' && (
              <div>
                <h2 className='text-2xl font-bold text-[#333] mb-2.5'>Research</h2>
                <div
                  className="ProseMirror"
                  dangerouslySetInnerHTML={{ __html: facultyMember.research || '<p>No research information available.</p>' }}
                />
              </div>
            )}
            {activeTab === 'interests' && (
              <div>
                <h2 className='text-2xl font-bold text-[#333] mb-2.5'>Research Interests</h2>
                <div
                  className="ProseMirror"
                  dangerouslySetInnerHTML={{ __html: facultyMember.interests || '<p>No research interests available.</p>' }}
                />
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default FacultyProfilePage;