import React from 'react';
import { <PERSON> } from '@inertiajs/react';
import { Facebook, Linkedin, Mail, Globe } from 'lucide-react';
import Back from '../../common/back/Back';
import { Governingbody } from '@/types';

// Define a placeholder for your reusable components

const Heading = ({ title }: { title: string }) => (
    <h2 className="my-12 text-center text-4xl font-bold text-gray-800">{title}</h2>
);

// Define the TypeScript interface for a governing body member
interface GBodyMember {
    data: Governingbody[];
}


// Reusable Card Component for a single member
const MemberCard = ({ member }: { member: Governingbody }) => (
    <div className="group w-full text-center sm:w-[45%] lg:w-[23%] m-2.5 overflow-hidden rounded-lg shadow-lg transition-all duration-300 ease-in-out hover:scale-105 hover:shadow-2xl">
        <div className="relative">
            <img src={member.profile_image} alt={member.name} className="h-auto w-full" />
            
        </div>
        <div className="p-4">
            <h3 className="mb-1 text-lg font-semibold text-gray-800 transition-colors group-hover:text-blue-700">
                <a href={member.profile_URL} target="_blank" rel="noopener noreferrer">
                    {member.name}
                </a>
            </h3>
            <p className="text-sm text-gray-600">{member.designation}</p>
        </div>
    </div>
);

// Helper for Social Icons



const GoverningBodyPage = ({governingbody}: {governingbody: GBodyMember}) => {
    
    console.log(governingbody);

 
    return (
        <>
            <Back title='Governing Body' />
            <section className="py-24">
                <div className="mx-auto flex w-full flex-col items-center px-4 sm:w-[85%]">
                
                    {/* New Governing Body Section */}
                    <Heading title={"Governing Body"} />
                    <div className="flex w-full justify-center">
                        {governingbody.data
                            .filter((gbody) => gbody.display_order === 1)
                            .map((gbody) => <MemberCard key={gbody.id} member={gbody} />)
                        }
                    </div>
                    <div className="flex w-full flex-wrap justify-center lg:justify-start">
                        {governingbody.data
                            .filter((gbody) => gbody.display_order !== 1)
                            .map((gbody) => <MemberCard key={gbody.id} member={gbody} />)
                        }
                    </div>

                   
                </div>
            </section>
        </>
    );
};

export default GoverningBodyPage;