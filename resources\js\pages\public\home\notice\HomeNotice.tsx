import { Noticeboard, HomepageAd } from '@/types';
import { Link } from '@inertiajs/react';

interface Notice {
    id: number;
    date: string;
    title: string;
    link: string;
}

interface NoticeData {
    data: Noticeboard[];
}

const HomeNotice = ({ notices, adData }: { notices: NoticeData, adData: HomepageAd | null }) => {
    console.log('notice  ad in home notice:', notices, adData);

    return (
        <section className="pt-16 pb-8">
            <div className="relative mx-auto flex w-[90%] flex-col gap-8 md:flex-row md:justify-between">
                {/* Admission Info */}
                <div className="w-full md:w-1/2">
                    <h1 className="text-center text-2xl font-bold text-green-900">ADMISSION INFO</h1>
                    <h2 className="mt-2 text-center text-2xl font-bold text-red-600">{adData?.program_info_title}</h2>
                    <h2 className="text-center text-2xl font-bold text-red-600">
                    {adData?.session}
                    </h2>
                    <h2 className="mt-4 text-center text-2xl font-bold text-[#000F38]">
                        {adData?.offer_title}
                    </h2>
                    <p className="mt-4 text-center text-sm text-[#000F38] md:text-base">
                        {adData?.offer_text}
                    </p>
                    <ul className="mt-4 space-y-2">
                        <li className="border-l-4 border-green-600 bg-white p-3 text-gray-800 shadow-sm">B.Sc Hons. (Professional) Degree</li>
                        <li className="border-l-4 border-green-600 bg-white p-3 text-gray-800 shadow-sm">Under National University</li>
                        <li className="border-l-4 border-green-600 bg-white p-3 text-gray-800 shadow-sm">Conducted By Daffodil</li>
                        <li className="border-l-4 border-green-600 bg-white p-3 text-gray-800 shadow-sm">Affordable Cost</li>
                    </ul>
                    <div className="mt-4 flex flex-wrap gap-3 sm:flex-nowrap sm:justify-start">
                        <Link href="/admission-info" className="rounded bg-green-700 px-6 py-2 text-sm text-white hover:bg-green-800">
                            DETAILS
                        </Link>
                        <Link href="/application-form" className="rounded bg-green-700 px-6 py-2 text-sm text-white hover:bg-green-800">
                            APPLY
                        </Link>
                    </div>
                </div>

                {/* Vertical Divider for larger screens */}
                <div className="hidden w-px bg-gray-300 md:block"></div>

                {/* Latest Notice */}
                <div className="w-full md:w-1/2">
                    <h3 className="mb-4 text-center text-2xl font-bold text-blue-900 md:text-left">Latest Notice</h3>
                    <ul className="space-y-3">
                        {notices.data.map((notice) => {
                            const [day, month] = notice.created_at.split(' ');
                            return (
                                <li
                                    key={notice.id}
                                    className="flex items-center justify-between rounded border border-dashed border-green-600 bg-gray-100 p-3"
                                >
                                    <div className="flex w-full items-center gap-4">
                                        <div className="flex h-16 w-16 flex-shrink-0 flex-col items-center justify-center rounded bg-green-600 text-center text-white">
                                            <span className="text-lg leading-none font-bold">{day}</span>
                                            <span className="text-sm leading-none">{month}</span>
                                        </div>

                                        <span className="flex-grow text-sm leading-snug text-gray-900 md:text-base">{notice.title}</span>

                                        <Link
                                            href={`/noticeboard/${notice.id}/${notice.slug}`}
                                            className="ml-auto shrink-0 rounded bg-green-600 px-4 py-2 text-sm text-white hover:bg-green-800"
                                        >
                                            View
                                        </Link>
                                    </div>
                                </li>
                            );
                        })}
                    </ul>
                    <div className="mt-4 flex justify-end">
                        <Link href="/noticeboard" className="rounded bg-green-600 px-6 py-2 text-sm text-white hover:bg-green-800">
                            View All
                        </Link>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default HomeNotice;
