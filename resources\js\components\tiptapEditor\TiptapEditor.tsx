import React, { useState, useEffect, useCallback } from 'react';
import { useEditor, EditorContent, Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import Youtube from '@tiptap/extension-youtube';
import { TableKit } from '@tiptap/extension-table';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import TableRow from '@tiptap/extension-table-row';


import { TipTapButton } from './TipTapButton';
import { TipTapDivider } from './TipTapDivider';
// Import the styles

// A helper for translation if you use one, otherwise just return the string
const __ = (text: string) => text;

// Props definition for our component
interface TiptapEditorProps {
  value: string;
  onChange: (value: string) => void;
  editorClass?: string;
  fileUploadUrl?: string;
  editorId?: string;
}

export const TiptapEditor: React.FC<TiptapEditorProps> = ({
  value,
  onChange,
  editorClass = '',
  fileUploadUrl,
  editorId = 'tiptap-editor',
}) => {
  const [codeMode, setCodeMode] = useState(false);
  const [htmlContent, setHtmlContent] = useState(value);
  const [showTableToolbar, setShowTableToolbar] = useState(false);

  const onUpdate = ({ editor }: { editor: Editor }) => {
    const html = editor.getHTML();
    onChange(html);
    setHtmlContent(html);
  };
  
  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      Link.configure({ openOnClick: false }),
      Image.configure({ inline: false }),
      Youtube.configure({ controls: false }),
      TableKit.configure({
        table: { resizable: true },
      }),
      TableRow,
      TableHeader,
      TableCell,
    ],
    content: value,
    onUpdate,
  });

  useEffect(() => {
    return () => {
      editor?.destroy();
    };
  }, [editor]);
  
  const setLink = useCallback(() => {
    if (!editor) return;
    const previousUrl = editor.getAttributes('link').href;
    const url = window.prompt('URL', previousUrl);
    if (url === null) return;
    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }
    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  }, [editor]);

  const addVideo = useCallback(() => {
    if (!editor) return;
    const url = window.prompt('Enter YouTube URL');
    if (url) {
      editor.commands.setYoutubeVideo({ src: url });
    }
  }, [editor]);
  
  const uploadImage = useCallback(() => {
    if (!fileUploadUrl || !editor) return;

    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
            const formData = new FormData();
            formData.append('image', file);
            
            fetch(fileUploadUrl, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': (document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement)?.content,
                     'Accept': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.fileUrl) {
                    editor.chain().focus().setImage({ src: data.fileUrl }).run();
                }
            })
            .catch(error => {
                console.error('Error uploading image:', error);
                window.alert('Error uploading image.');
            });
        }
    };
    input.click();
  }, [editor, fileUploadUrl]);

  const changeEditorMode = () => {
    if (!editor) return;
    const newMode = !codeMode;
    setCodeMode(newMode);

    if (newMode) {
      // Switching to code mode, get latest HTML from editor
      setHtmlContent(editor.getHTML());
    } else {
      // Switching back to editor mode, set content from textarea
      editor.commands.setContent(htmlContent);
    }
  };
  
  if (!editor) {
    return null;
  }
  
  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setHtmlContent(e.target.value);
    // Also update the parent component's state if needed while in code mode
    onChange(e.target.value);
  }

  // Toolbar Component for better organization
  const MainToolbar = () => (
    <div className={`border-neutral-7 mx-0 mt-1 mb-0 flex flex-none flex-wrap items-center rounded-t-md border-x border-t border-solid bg-no-repeat p-2 font-sans text-xl leading-5 tracking-normal break-words ${editorClass}`}>
        <TipTapButton title={__('Bold')} icon="ri-bold" onClick={() => editor.chain().focus().toggleBold().run()} />
        <TipTapButton title={__('Italic')} icon="ri-italic" onClick={() => editor.chain().focus().toggleItalic().run()} />
        <TipTapButton title={__('Underline')} icon="ri-underline" onClick={() => editor.chain().focus().toggleUnderline().run()} />
        <TipTapButton title={__('Strikethrough')} icon="ri-strikethrough-2" onClick={() => editor.chain().focus().toggleStrike().run()} />
        <TipTapDivider />
        <TipTapButton title={__('Heading 1')} icon="ri-h-1" onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()} />
        <TipTapButton title={__('Heading 2')} icon="ri-h-2" onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()} />
        <TipTapButton title={__('Heading 3')} icon="ri-h-3" onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()} />
        <TipTapButton title={__('Paragraph')} icon="ri-paragraph" onClick={() => editor.chain().focus().setParagraph().run()} />
        <TipTapButton title={__('List')} icon="ri-list-unordered" onClick={() => editor.chain().focus().toggleBulletList().run()} />
        <TipTapButton title={__('Ordered Link')} icon="ri-list-ordered" onClick={() => editor.chain().focus().toggleOrderedList().run()} />
        <TipTapButton title={__('Add Link')} icon="ri-link-m" onClick={setLink} />
        <TipTapDivider />
        {fileUploadUrl && <TipTapButton title={__('Add Image')} icon="ri-image-add-line" onClick={uploadImage} />}
        <TipTapButton title={__('Add Video')} icon="ri-youtube-line" onClick={addVideo} />
        <TipTapButton title={__('Table')} icon="ri-table-line" onClick={() => setShowTableToolbar(!showTableToolbar)} />
        <TipTapDivider />
        <TipTapButton title={__('Undo')} icon="ri-arrow-go-back-line" onClick={() => editor.chain().focus().undo().run()} />
        <TipTapButton title={__('Redo')} icon="ri-arrow-go-forward-line" onClick={() => editor.chain().focus().redo().run()} />
        <TipTapDivider />
        <TipTapButton title={__('Code View')} icon="ri-code-box-line" onClick={changeEditorMode} />
    </div>
  );

  const TableToolbar = () => (
    <div className="border-neutral-7 border-t-neutral-7 mx-0 mb-0 flex flex-none flex-wrap items-center border-x border-t border-solid bg-no-repeat p-2 font-sans text-xl leading-5 tracking-normal break-words">
        <TipTapButton title={__('Insert Table')} icon="ri-table-2" onClick={() => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run()} />
        <TipTapDivider />
        <TipTapButton title={__('Add Column Before')} icon="ri-layout-3-line" onClick={() => editor.chain().focus().addColumnBefore().run()} />
        <TipTapButton title={__('Add Column After')} icon="ri-layout-6-line" onClick={() => editor.chain().focus().addColumnAfter().run()} />
        <TipTapButton title={__('Delete Column')} icon="ri-delete-column" onClick={() => editor.chain().focus().deleteColumn().run()} />
        <TipTapDivider />
        <TipTapButton title={__('Add Row Before')} icon="ri-insert-row-top" onClick={() => editor.chain().focus().addRowBefore().run()} />
        <TipTapButton title={__('Add Row After')} icon="ri-insert-row-bottom" onClick={() => editor.chain().focus().addRowAfter().run()} />
        <TipTapButton title={__('Delete Row')} icon="ri-delete-row" onClick={() => editor.chain().focus().deleteRow().run()} />
        <TipTapDivider />
        <TipTapButton title={__('Delete Table')} icon="ri-delete-bin-2-line" onClick={() => editor.chain().focus().deleteTable().run()} />
    </div>
  );

  return (
    <div>
      <MainToolbar />
      {showTableToolbar && <TableToolbar />}
      
      <div style={{ display: codeMode ? 'none' : 'block' }}>
        <EditorContent editor={editor} className="border-neutral-7 relative m-0 max-h-[240px] min-h-[120px] overflow-auto rounded-b-md border border-solid bg-no-repeat px-1 py-1 font-sans text-xs leading-5 tracking-normal break-words" />
      </div>

      <textarea
        id={editorId}
        value={htmlContent}
        onChange={handleTextAreaChange}
        style={{ display: codeMode ? 'block' : 'none' }}
        className="border-neutral-7 bg-neutral-1 min-h-[240px] w-full rounded-b-md border border-solid font-sans text-xs leading-5 tracking-normal"
      />
    </div>
  );
};