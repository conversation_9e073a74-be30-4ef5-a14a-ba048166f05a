import React, { useState } from 'react';
import { facultyData } from './FacultyCard'; // Import faculty data
//import { useParams } from 'react-router-dom'; // Get the faculty ID from the route
import './profileview.css'; // Import the CSS file
import Back from '../../common/back/Back';

const ProfileView = () => {
  const { id } = useParams(); // Get faculty ID from URL parameters
  const faculty = facultyData.find((faculty) => faculty.id === id); // Find faculty member

  const [activeTab, setActiveTab] = useState('about'); // State to manage the active tab

  if (!faculty) {
    return <h2>Faculty member not found</h2>; // Handle case when faculty is not found
  }

  const handleTabChange = (tab) => {
    setActiveTab(tab); // Set the selected tab
  };

  return (
    <>
      <Back title="Faculty Profile" />
      <div className="faculty-profile-container">
        {/* Top Section: Profile Image and Details */}
        <div className="profile-header">
          <div className="profile-image">
            <img src={faculty.image} alt={faculty.name} />
          </div>
          <div className="profile-details">
            <h2>{faculty.name}</h2>
            <p><strong>Designation: </strong>{faculty.title}</p>
            <p><strong>Department: </strong>{faculty.dept}</p>
            <p className="content-justify">{faculty.bio}</p>
            <div className="contacts">
              <p><strong>Email: </strong>{faculty.social.mail}</p>
              <p><strong>Phone: </strong>{faculty.social.phone}</p>
            </div>
          </div>
        </div>
        
      </div>
      
      {/* Tabbed Pane Section */}
      <div className="faculty-profile-container">
        <div className="tabs">
          <button
            className={activeTab === 'about' ? 'active' : ''}
            onClick={() => handleTabChange('about')}
          >
            About
          </button>
          <button
            className={activeTab === 'education' ? 'active' : ''}
            onClick={() => handleTabChange('education')}
          >
            Education
          </button>
          <button
            className={activeTab === 'experience' ? 'active' : ''}
            onClick={() => handleTabChange('experience')}
          >
            Research Experience
          </button>
          <button
            className={activeTab === 'interest' ? 'active' : ''}
            onClick={() => handleTabChange('interest')}
          >
            Research Interests
          </button>
        </div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === 'about' && (
            <div>
              <h3>About</h3>
              <p className="content-justify">{faculty.about}</p>
            </div>
          )}
          {activeTab === 'education' && (
            <div>
              <h3>Education</h3>
              <ul>
                {faculty.education.map((edu, index) => (
                  <li key={index}>{edu}</li>
                ))}
              </ul>
            </div>
          )}
          {activeTab === 'experience' && (
            <div>
              <h3>Research Experience</h3>
              <ul>
                {faculty.experience?.map((edu, index) => (
                  <li key={index}>{edu}</li>
                ))}
              </ul>
            </div>
          )}
          {activeTab === 'interest' && (
            <div>
              <h3>Research Interests</h3>
              <p>{faculty.interests}</p>
            </div>
          )}
        </div>
      
      </div>


    </>
  );
};

export default ProfileView;
