import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Head, Link } from '@inertiajs/react';
import { Calendar, ChevronsRight, Tag } from 'lucide-react';
import { useMemo, useState } from 'react';
import Back from '../common/back/Back';

// Mock data for notices
import Pagination from '@/components/pagination';
import { Noticeboard } from '@/types';
import { Button } from '@/components/ui/button';

interface PaginatedNoticeboardData {
    data: Noticeboard[];
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
}
const filterOptions = [
    { value: 'all', label: 'All Categories' },
    { value: 'cse', label: 'CSE' },
    { value: 'bba', label: 'B<PERSON>' },
    { value: 'bmb', label: 'BMB' },
    { value: 'ece', label: 'ECE' },
    { value: 'academic', label: 'Academic' },
    { value: 'official', label: 'Official' },
];

export default function NoticeboardPage({ noticeBoardData }: { noticeBoardData: PaginatedNoticeboardData }) {
    console.log('noticeBoardData:', noticeBoardData);

    const [selectedFilter, setSelectedFilter] = useState('all');

    const filteredNotices = useMemo(() => {
        if (selectedFilter === 'all') {
            return noticeBoardData.data;
        }
        return noticeBoardData.data.filter((notice) => notice.category === selectedFilter);
    }, [noticeBoardData, selectedFilter]);

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
        });
    };

    const getCategoryLabel = (category: string) => {
        if (category) {
            const option = filterOptions.find((opt) => opt.value === category);
            return option ? option.label : category.toUpperCase();
        } else {
            return 'No Category';
        }
    };

    const getCategoryColor = (category: string) => {
        const colors = {
            cse: 'bg-blue-100 text-blue-800',
            bba: 'bg-green-100 text-green-800',
            bmb: 'bg-purple-100 text-purple-800',
            ece: 'bg-orange-100 text-orange-800',
            academic: 'bg-indigo-100 text-indigo-800',
            official: 'bg-red-100 text-red-800',
        };
        return colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800';
    };

    return (
        <>
            <Head title="Notice Board" />
            <Back title="Notice Board" />

            <div className="min-h-screen bg-gray-50 p-6">
                <div className="mx-auto max-w-4xl">
                    <div className="mb-8">
                        <h1 className="mb-2 text-3xl font-bold text-gray-900">Notice Board</h1>
                        <p className="text-gray-600">Stay updated with the latest announcements and notices</p>
                    </div>

                    {/* Filter Section */}
                    <div className="mb-6">
                        <div className="flex items-center gap-3">
                            <label htmlFor="category-filter" className="text-sm font-medium text-gray-700">
                                Filter by Category:
                            </label>
                            <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                                <SelectTrigger className="w-48">
                                    <SelectValue placeholder="Select category" />
                                </SelectTrigger>
                                <SelectContent>
                                    {filterOptions.map((option) => (
                                        <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Results Count */}
                    <div className="mb-4">
                        <p className="text-sm text-gray-600">
                            Showing {filteredNotices.length} {filteredNotices.length === 1 ? 'notice' : 'notices'}
                            {selectedFilter !== 'all' && ` in ${getCategoryLabel(selectedFilter)}`}
                        </p>
                    </div>

                    {/* Notices List */}
                    <div className="space-y-4">
                        {filteredNotices.length === 0 ? (
                            <Card className="p-8 text-center">
                                <p className="text-gray-500">No notices found for the selected category.</p>
                            </Card>
                        ) : (
                            filteredNotices.map((notice) => (
                                <Card key={notice.id} className="transition-shadow duration-200 hover:shadow-md">
                                    <CardContent className="p-6">
                                        <div className="space-y-3">
                                            {/* Title Section */}
                                            <div className="flex items-center gap-1">
                                                <ChevronsRight />
                                                <Link
                                                    href={`/noticeboard/${notice.id}/${notice.slug}`}
                                                    className="line-clamp-2 text-lg font-semibold text-gray-900 transition-colors duration-200 hover:text-blue-600"
                                                >
                                                    {notice.title}
                                                </Link>
                                            </div>

                                            {/* Metadata Section */}
                                            <div className="flex items-center gap-4 text-sm text-gray-600">
                                                <div className="flex items-center gap-1">
                                                    <Calendar className="h-4 w-4" />
                                                    <span>{formatDate(notice.created_at)}</span>
                                                </div>
                                                <div className="flex items-center gap-1">
                                                    <Tag className="h-4 w-4" />
                                                    <span
                                                        className={`rounded-full px-2 py-1 text-xs font-medium ${getCategoryColor(notice.category)}`}
                                                    >
                                                        {getCategoryLabel(notice.category)}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))
                        )}
                    </div>

                    <div className="mt-8 mb-8">
                        <Pagination meta={noticeBoardData.meta} />
                    </div>
                   
                </div>
            </div>
        </>
    );
}
