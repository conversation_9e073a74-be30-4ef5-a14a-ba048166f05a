.faculty-list-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: 85%;
    margin: 0 auto;
  }
  
.faculty-list {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    width: 100%;
}

/* Center the first row */
.centered-row {
    justify-content: center; /* Center the first row */
}

/* Align second row to the left */
.left-aligned-row {
    justify-content: flex-start; /* Align items in the second row to the left */
}

  .faculty-card {
    position: relative;
    width: 23%;
    margin: 10px 10px 10px 10px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-align: center;
  }
  
  .faculty-card:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
  
  .faculty-image img {
    width: 100%;
    height: auto;
  }
  
  .faculty-image {
    position: relative;
  }
  
  .social-icons {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  .social-icons a {
    background-color: hsl(234.69deg 61.81% 9.73% / 70%);
    border-radius: 50%;
    color: white;
    height: 40px;
    line-height: 40px;
    text-align: center;
    width: 40px;
    transition: background-color 0.3s ease;
  }
  
  .social-icons a:hover {
    color: white;
    background-color: #0a08a5;
    transition: background-color 0.5s ease;
  }
  
  .faculty-image:hover .social-icons {
    opacity: 1;
  }
  
  .faculty-info {
    padding: 15px;
  }
  
  .faculty-name {
    font-size: 18px;
    margin-bottom: 5px;
    cursor: pointer;
    color: #333;
  }
  
  .faculty-name.clickable:hover {
    color: #0073b1; /* Make the name clickable with a hover effect */
  }
  
  .faculty-info p {
    font-size: 14px;
    color: #666;
  }

  /* profile view start*/
  .faculty-profile {
    text-align: center;
    margin: 20px auto;
    max-width: 600px;
  }
  
  .faculty-profile img {
    width: 50%;
    height: auto;
    border-radius: 10px;
  }
  
  .faculty-profile h2 {
    font-size: 28px;
    margin: 10px 0;
  }
  
  .faculty-profile p {
    font-size: 18px;
    margin: 5px 0;
    color: #333;
  }
  
  .social-links a {
    margin: 10px;
    display: inline-block;
    font-size: 20px;
    color: #0073b1;
  }
  
  .social-links a:hover {
    color: #555;
  }
  
  /*end*/
  
  @media screen and (max-width: 768px) {
    .faculty-list-container{
        justify-content: center;
        width: 100%;
    }
    .faculty-card {
      width: 85%;
    }
    .left-aligned-row {
        justify-content: center; 
    }
  }
  
  @media screen and (max-width: 480px) {
    .faculty-list-container{
        justify-content: center;
        width: 100%;
    }
    .faculty-card {
      width: 90%;
    }
    .left-aligned-row {
        justify-content: center; 
    }
  }
  