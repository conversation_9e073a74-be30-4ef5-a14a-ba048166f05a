import React from 'react';
import 'remixicon/fonts/remixicon.css';

// Define the props interface for type safety
interface TipTapButtonProps {
  title: string;
  icon: string;
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

export const TipTapButton: React.FC<TipTapButtonProps> = ({ title, icon, onClick }) => {
  return (
    <button
      title={title}
      onClick={onClick}
      className="text-neutral-11 hover:bg-neutral-3 hover:text-neutral-12 m-0 h-6 w-6 cursor-pointer overflow-visible border-none bg-transparent bg-no-repeat p-0 text-center font-sans text-sm leading-5 break-words normal-case hover:rounded-xs"
    >
      <i className={` text-center text-sm m-0 cursor-pointer bg-no-repeat p-0 leading-5 break-words not-italic ${icon}`}></i>
    </button>
  );
};