import React from 'react';
import InertiaPagination from '@/components/intertia-pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { Search, Eye, Edit, Trash2 } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';
import Pagination from '@/components/pagination';


const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Governing Body',
        href: '/governingbody',
    },
];

interface GoverningbodyData {
    id: number;
    name: string;
    designation: string;
    display_order: number;
    profile_URL: string;
    profile_image: string;
}

interface PaginatedGoverningbodyData {
    data: GoverningbodyData[];
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
 
}


interface Flash {
    success?: string;
    danger?: string;
}


 

interface GoverningBodyIndexProps {
    governingbodyData: PaginatedGoverningbodyData;
    filters: {
        search?: string;
    };
}

export default function GoverningBodyIndex({ governingbodyData, filters }: GoverningBodyIndexProps) {
    const { flash } = usePage<{ flash: Flash }>().props; //getting flash message from the server side and passing it to the client side
    const { showConfirmation } = useConfirmation();
    const [viewingGoverningBody, setViewingGoverningBody] = useState<GoverningbodyData | null>(null);
    const [searchValue, setSearchValue] = useState<string>(filters.search || '');
    console.log(governingbodyData); //logging the data to the console
    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }
    
        if (flash.danger) {
            toast.error(flash.danger);
        }
        }, [flash]);
    
        // Search functionality
    const handleSearch = useRef(
        debounce((query: string) => {
            router.get(route('admin.governingbody.index'), { search: query }, { preserveState: true, replace: true });
        }, 500),
    ).current;

    // search method
    function onSearchChange(e: React.ChangeEvent<HTMLInputElement>) {
        const query = e.target.value;
        setSearchValue(query);
        handleSearch(query);
    }

    // clear search
    const clearSearch = () => {
        setSearchValue('');
        handleSearch('');
    };

    // View handler
    const handleViewGoverningBody = (item: GoverningbodyData) => {
        setViewingGoverningBody(item);
    };

    // Delete handler with confirmation
    const handleDeleteGoverningBody = async (id: number, name: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Governing Body Member',
            description: `Are you sure you want to delete ${name}? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.governingbody.destroy', id));
        }
    };

    
  return (
    <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Governing Body" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="relative w-full sm:w-1/3">
                            <Input
                                id={'search'}
                                className="peer ps-9 pr-9"
                                placeholder="Search by name or designation..."
                                type="search"
                                value={searchValue}
                                onChange={onSearchChange}
                            />
                            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
                                <Search size={16} aria-hidden="true" />
                            </div>
                            
                        </div>

                        <Button>
                            <Link href={route('admin.governingbody.create')} prefetch>
                                Add Entry
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>#</TableHead>
                                        <TableHead>Image</TableHead>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Designation</TableHead>
                                        <TableHead>Display Order</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {governingbodyData.data.map((dataItem, index) => (
                                        <TableRow key={dataItem.id}>
                                            <TableCell>{governingbodyData.meta.from + index }</TableCell>
                                            <TableCell>
                                                <img src={dataItem.profile_image} alt={dataItem.name} className="w-11 rounded" />
                                            </TableCell>
                                            <TableCell>{dataItem.name}</TableCell>
                                            <TableCell>{dataItem.designation}</TableCell>
                                            <TableCell>{dataItem.display_order}</TableCell>
                                           
                                            {/*<TableCell>
                                                {post.status == '0' ? (
                                                    <Badge className="bg-red-500">Inactive</Badge>
                                                ) : (
                                                    <Badge className="bg-green-500">Active</Badge>
                                                )}
                                            </TableCell>*/}
                                            <TableCell className="space-x-1">
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="outline" onClick={() => handleViewGoverningBody(dataItem)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>View</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="outline" asChild>
                                                            <Link href={route('admin.governingbody.edit', dataItem.id)} prefetch>
                                                                <Edit className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Edit</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="destructive" onClick={() => handleDeleteGoverningBody(dataItem.id, dataItem.name)}>
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Delete</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                    <div className="mt-4">
                        <Pagination meta={governingbodyData.meta} />
                    </div>

                    {/* View Governing Body Dialog */}
                    <Dialog open={!!viewingGoverningBody} onOpenChange={() => setViewingGoverningBody(null)}>
                        <DialogContent className="sm:max-w-lg">
                            <DialogHeader>
                                <DialogTitle>View Governing Body Member</DialogTitle>
                            </DialogHeader>
                            {viewingGoverningBody && (
                                <div className="space-y-4">
                                    <div className="flex items-center gap-4">
                                        <img
                                            src={viewingGoverningBody.profile_image}
                                            alt={viewingGoverningBody.name}
                                            className="w-20 h-20 rounded-full object-cover"
                                        />
                                        <div>
                                            <h3 className="text-xl font-bold">{viewingGoverningBody.name}</h3>
                                            <p className="text-gray-600">{viewingGoverningBody.designation}</p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label>ID</Label>
                                            <p className="text-sm font-medium">{viewingGoverningBody.id}</p>
                                        </div>
                                        <div>
                                            <Label>Display Order</Label>
                                            <p className="text-sm">{viewingGoverningBody.display_order}</p>
                                        </div>
                                    </div>

                                    <div>
                                        <Label>Profile URL</Label>
                                        {viewingGoverningBody.profile_URL ? (
                                            <a
                                                href={viewingGoverningBody.profile_URL}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-sm text-blue-600 hover:underline break-all"
                                            >
                                                {viewingGoverningBody.profile_URL}
                                            </a>
                                        ) : (
                                            <p className="text-sm text-gray-500">No profile URL provided</p>
                                        )}
                                    </div>
                                </div>
                            )}
                        </DialogContent>
                    </Dialog>
                </div>
            </div>
        </AppLayout>
  )
}



