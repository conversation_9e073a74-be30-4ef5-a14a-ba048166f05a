import React from 'react';
import InertiaPagination from '@/components/intertia-pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { Search, Eye, Edit, Trash2 } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';
import Pagination from '@/components/pagination';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Faculty',
        href: '/faculty',
    },
];

interface FacultyData {
    id: number;
    name: string;
    slug: string;
    designation: string;
    department: string;
    image: string;
    bio: string;
    about: string;
    education: string;
    research: string;
    interests: string;
    official_email: string;
    secondary_email: string;
    primary_phone: string;
    secondary_phone: string;
}

interface PaginatedFacultyData {
    data: FacultyData[];
    links: {
        url: string | null;
        label: string;
        active: boolean;
    }[];
    meta: {
        links: {
            url: string | null;
            label: string;
            active: boolean;
        }[];
        total: number;
        from: number;
        to: number;
    };
}

interface Flash {
    success?: string;
    danger?: string;
}

export default function FacultyIndex({ facultyData }: { facultyData: PaginatedFacultyData }) {
    const { flash } = usePage<{ flash: Flash }>().props;
    const { showConfirmation } = useConfirmation();
    const [searchTerm, setSearchTerm] = useState('');
    const [viewingFaculty, setViewingFaculty] = useState<FacultyData | null>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    const debouncedSearch = debounce((term: string) => {
        router.get(route('admin.faculty.index'), { search: term }, { preserveState: true, replace: true });
    }, 300);

    const onSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    // View handler
    const handleViewFaculty = (faculty: FacultyData) => {
        setViewingFaculty(faculty);
    };

    // Delete handler with confirmation
    const handleDeleteFaculty = async (id: number, name: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Faculty',
            description: `Are you sure you want to delete ${name}? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.faculty.destroy', id));
        }
    };

    useEffect(() => {
        if (flash.success) {
            toast.success(flash.success);
        }

        if (flash.danger) {
            toast.error(flash.danger);
        }
    }, [flash]);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Faculty" />
            <div className="flex h-full flex-1 flex-col gap-4 rounded-xl p-4">
                <div className="rounded border p-6 shadow-xl">
                    <div className="mb-5 flex items-center justify-between">
                        <div className="relative w-full sm:w-1/3">
                            <Input 
                                id={'search'} 
                                className="peer ps-9" 
                                placeholder="Search..." 
                                type="search" 
                                onChange={onSearchChange} 
                            />
                            <div className="text-muted-foreground/80 pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 peer-disabled:opacity-50">
                                <Search size={16} aria-hidden="true" />
                            </div>
                        </div>

                        <Button>
                            <Link href={route('admin.faculty.create')} prefetch>
                                Add Faculty
                            </Link>
                        </Button>
                    </div>

                    <Card>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>#</TableHead>
                                        <TableHead>Image</TableHead>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Designation</TableHead>
                                        <TableHead>Department</TableHead>
                                        <TableHead>Phone</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {facultyData.data.map((dataItem, index) => (
                                        <TableRow key={dataItem.id}>
                                            <TableCell>{facultyData.meta.from + index }</TableCell>
                                            <TableCell>
                                                {dataItem.image ? (
                                                    <img src={dataItem.image} alt={dataItem.name} className="w-11 rounded" />
                                                ) : (
                                                    <div className="w-11 h-11 bg-gray-200 rounded flex items-center justify-center">
                                                        <span className="text-xs text-gray-500">No Image</span>
                                                    </div>
                                                )}
                                            </TableCell>
                                            <TableCell>{dataItem.name}</TableCell>
                                            <TableCell>{dataItem.designation}</TableCell>
                                            <TableCell>{dataItem.department}</TableCell>
                                            <TableCell>{dataItem.primary_phone}</TableCell>
                                            <TableCell className="space-x-1">
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="outline" onClick={() => handleViewFaculty(dataItem)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>View</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="outline" asChild>
                                                            <Link href={route('admin.faculty.edit', dataItem.id)} prefetch>
                                                                <Edit className="h-4 w-4" />
                                                            </Link>
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Edit</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="destructive" onClick={() => handleDeleteFaculty(dataItem.id, dataItem.name)}>
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Delete</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                    <div className="mt-4">
                        <Pagination meta={facultyData.meta} />
                    </div>

                    {/* View Faculty Dialog */}
                    <Dialog open={!!viewingFaculty} onOpenChange={() => setViewingFaculty(null)}>
                        <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>View Faculty Profile</DialogTitle>
                            </DialogHeader>
                            {viewingFaculty && (
                                <div className="space-y-6">
                                    {/* Basic Info */}
                                    <div className="flex items-center gap-4">
                                        {viewingFaculty.image ? (
                                            <img
                                                src={viewingFaculty.image}
                                                alt={viewingFaculty.name}
                                                className="w-20 h-20 rounded-full object-cover"
                                            />
                                        ) : (
                                            <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                                                <span className="text-sm text-gray-500">No Image</span>
                                            </div>
                                        )}
                                        <div>
                                            <h3 className="text-xl font-bold">{viewingFaculty.name}</h3>
                                            <p className="text-gray-600">{viewingFaculty.designation}</p>
                                            <p className="text-gray-600">{viewingFaculty.department}</p>
                                        </div>
                                    </div>

                                    {/* Bio */}
                                    <div>
                                        <Label>Bio</Label>
                                        <p className="text-sm text-gray-700 mt-1">{viewingFaculty.bio || 'No bio available'}</p>
                                    </div>

                                    {/* About */}
                                    <div>
                                        <Label>About</Label>
                                        <div
                                            className="text-sm text-gray-700 mt-1 prose prose-sm max-w-none"
                                            dangerouslySetInnerHTML={{
                                                __html: viewingFaculty.about || '<p>No information available</p>'
                                            }}
                                        />
                                    </div>

                                    {/* Contact Information */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label>Official Email</Label>
                                            <p className="text-sm">{viewingFaculty.official_email}</p>
                                        </div>
                                        <div>
                                            <Label>Secondary Email</Label>
                                            <p className="text-sm">{viewingFaculty.secondary_email || 'N/A'}</p>
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <Label>Primary Phone</Label>
                                            <p className="text-sm">{viewingFaculty.primary_phone}</p>
                                        </div>
                                        <div>
                                            <Label>Secondary Phone</Label>
                                            <p className="text-sm">{viewingFaculty.secondary_phone || 'N/A'}</p>
                                        </div>
                                    </div>

                                    {/* Education */}
                                    <div>
                                        <Label>Education</Label>
                                        <div
                                            className="text-sm text-gray-700 mt-1 prose prose-sm max-w-none"
                                            dangerouslySetInnerHTML={{
                                                __html: viewingFaculty.education || '<p>No education information available</p>'
                                            }}
                                        />
                                    </div>

                                    {/* Research */}
                                    <div>
                                        <Label>Research</Label>
                                        <div
                                            className="text-sm text-gray-700 mt-1 prose prose-sm max-w-none"
                                            dangerouslySetInnerHTML={{
                                                __html: viewingFaculty.research || '<p>No research information available</p>'
                                            }}
                                        />
                                    </div>

                                    {/* Interests */}
                                    <div>
                                        <Label>Research Interests</Label>
                                        <div
                                            className="text-sm text-gray-700 mt-1 prose prose-sm max-w-none"
                                            dangerouslySetInnerHTML={{
                                                __html: viewingFaculty.interests || '<p>No interests listed</p>'
                                            }}
                                        />
                                    </div>
                                </div>
                            )}
                        </DialogContent>
                    </Dialog>
                </div>
            </div>
        </AppLayout>
    );
}
