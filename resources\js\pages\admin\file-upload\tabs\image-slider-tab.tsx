import InputError from '@/components/input-error';
import Pagination from '@/components/pagination';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useConfirmation } from '@/contexts/confirmation-context';
import { PaginatedData } from '@/types';
import { router, useForm } from '@inertiajs/react';
import debounce from 'lodash/debounce';
import { Edit, Eye, Plus, Search, Trash2 } from 'lucide-react';
import React, { useRef, useState } from 'react';

interface ImageSliderData {
    id: number;
    title: string;
    image: string;
    heading: string;
    caption: string;
    order: number;
    created_at: string;
    updated_at: string;
}

interface ImageSliderProps {
    data: PaginatedData<ImageSliderData>;
}

export default function ImageSliderTab({ data }: ImageSliderProps) {
    const { showConfirmation } = useConfirmation();
    const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editingItem, setEditingItem] = useState<ImageSliderData | null>(null);
    const [viewingItem, setViewingItem] = useState<ImageSliderData | null>(null);
    const [searchTerm, setSearchTerm] = useState('');
    const searchInputRef = useRef<HTMLInputElement>(null);
    console.log('imageSliders:', data);

    const {
        data: formData,
        setData,
        post,
        processing,
        errors,
        reset,
    } = useForm({
        title: '',
        image: null as File | null,
        heading: '',
        caption: '',
        order: 1,
    });

    const debouncedSearch = debounce((term: string) => {
        router.get(
            route('admin.file-upload.index'),
            {
                tab: 'image-slider',
                slider_search: term || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    }, 300);

    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        debouncedSearch(value);
    };

    const handleCreateSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.image-sliders.store'), {
            onSuccess: () => {
                setIsCreateDialogOpen(false);
                reset();
            },
        });
    };

    const handleEditSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingItem) {
            router.post(
                route('admin.image-sliders.update', editingItem.id),
                {
                    _method: 'put',
                    ...formData,
                },
                {
                    onSuccess: () => {
                        setIsEditDialogOpen(false);
                        setEditingItem(null);
                        reset();
                    },
                },
            );
        }
    };

    const handleEdit = (item: ImageSliderData) => {
        setEditingItem(item);
        setData({
            title: item.title,
            image: null,
            heading: item.heading,
            caption: item.caption,
            order: item.order,
        });
        setIsEditDialogOpen(true);
    };

    const handleDelete = async (id: number, title: string) => {
        const confirmed = await showConfirmation({
            title: 'Delete Image Slider Item',
            description: `Are you sure you want to delete "${title}"? This action cannot be undone.`,
            confirmText: 'Delete',
            variant: 'destructive',
        });

        if (confirmed) {
            router.delete(route('admin.image-sliders.destroy', id));
        }
    };

    const handleView = (item: ImageSliderData) => {
        setViewingItem(item);
    };

    return (
        <div className="space-y-6">
            {/* Search and Create Button */}
            <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
                <div className="relative w-full sm:w-1/3">
                    <Input
                        id={'search'}
                        className="peer ps-9"
                        placeholder="Search by title or heading..."
                        type="search"
                        value={searchTerm}
                        onChange={handleSearchChange}
                    />
                    <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-3 text-muted-foreground/80 peer-disabled:opacity-50">
                        <Search size={16} aria-hidden="true" />
                    </div>
                </div>

                <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                    <DialogTrigger asChild>
                        <Button>
                            <Plus className="mr-2 h-4 w-4" />
                            Add Image Slider
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md">
                        <DialogHeader>
                            <DialogTitle>Create New Image Slider</DialogTitle>
                        </DialogHeader>
                        <form onSubmit={handleCreateSubmit} className="space-y-4">
                            <div>
                                <Label htmlFor="create-title">Title *</Label>
                                <Input
                                    id="create-title"
                                    value={formData.title}
                                    onChange={(e) => setData('title', e.target.value)}
                                    placeholder="Enter image title"
                                    required
                                />
                                <InputError message={errors.title} />
                            </div>
                            <div>
                                <Label htmlFor="create-image">Image *</Label>
                                <Input
                                    id="create-image"
                                    type="file"
                                    accept="image/*"
                                    onChange={(e) => setData('image', e.target.files?.[0] || null)}
                                    required
                                />
                                <InputError message={errors.image} />
                            </div>
                            <div>
                                <Label htmlFor="create-heading">Heading *</Label>
                                <Input
                                    id="create-heading"
                                    value={formData.heading}
                                    onChange={(e) => setData('heading', e.target.value)}
                                    placeholder="Enter image heading"
                                    required
                                />
                                <InputError message={errors.heading} />
                            </div>
                            <div>
                                <Label htmlFor="create-caption">Caption *</Label>
                                <Textarea
                                    id="create-caption"
                                    value={formData.caption}
                                    onChange={(e) => setData('caption', e.target.value)}
                                    placeholder="Enter image caption"
                                    rows={3}
                                    required
                                />
                                <InputError message={errors.caption} />
                            </div>
                            <div>
                                <Label htmlFor="create-order">Display Order *</Label>
                                <Input
                                    id="create-order"
                                    type="number"
                                    min="1"
                                    value={formData.order}
                                    onChange={(e) => setData('order', parseInt(e.target.value) || 1)}
                                    required
                                />
                                <InputError message={errors.order} />
                            </div>
                            <div className="flex gap-2">
                                <Button type="submit" disabled={processing}>
                                    {processing ? 'Creating...' : 'Create'}
                                </Button>
                                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                                    Cancel
                                </Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>

            {/* Image Sliders Table */}
            <Card>
                <CardContent className="p-0">
                    {data.data.length > 0 ? (
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Order</TableHead>
                                    <TableHead>Image</TableHead>
                                    <TableHead>Title</TableHead>
                                    <TableHead>Created</TableHead>
                                    <TableHead>Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {data.data.map((item) => (
                                    <TableRow key={item.id}>
                                        <TableCell>
                                            <span className="font-medium text-blue-600">#{item.order}</span>
                                        </TableCell>
                                        <TableCell>
                                            <img src={item.image} alt={item.title} className="h-16 w-16 rounded border object-cover" />
                                        </TableCell>
                                        <TableCell>
                                            <div className="max-w-xs">
                                                <p className="truncate font-medium">{item.title}</p>
                                            </div>
                                        </TableCell>

                                        <TableCell>{item.created_at}</TableCell>
                                        <TableCell>
                                            <div className="flex space-x-1">
                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="outline" onClick={() => handleView(item)}>
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>View</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Edit</p>
                                                    </TooltipContent>
                                                </Tooltip>

                                                <Tooltip>
                                                    <TooltipTrigger asChild>
                                                        <Button size="sm" variant="destructive" onClick={() => handleDelete(item.id, item.title)}>
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </TooltipTrigger>
                                                    <TooltipContent>
                                                        <p>Delete</p>
                                                    </TooltipContent>
                                                </Tooltip>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    ) : (
                        <div className="py-12 text-center">
                            <p className="mb-4 text-gray-500">No image sliders data found</p>
                        </div>
                    )}
                </CardContent>
            </Card>

            <Pagination meta={data.meta} />
            {/* Edit Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                        <DialogTitle>Edit Image Slider</DialogTitle>
                    </DialogHeader>
                    <form onSubmit={handleEditSubmit} className="space-y-4">
                        <div>
                            <Label htmlFor="edit-title">Title *</Label>
                            <Input
                                id="edit-title"
                                value={formData.title}
                                onChange={(e) => setData('title', e.target.value)}
                                placeholder="Enter image title"
                                required
                            />
                            <InputError message={errors.title} />
                        </div>
                        <div>
                            <Label htmlFor="edit-image">Image</Label>
                            <Input id="edit-image" type="file" accept="image/*" onChange={(e) => setData('image', e.target.files?.[0] || null)} />
                            <p className="mt-1 text-sm text-gray-500">Leave empty to keep current image</p>
                            <InputError message={errors.image} />
                        </div>
                        <div>
                            <Label htmlFor="edit-heading">Heading *</Label>
                            <Input
                                id="edit-heading"
                                value={formData.heading}
                                onChange={(e) => setData('heading', e.target.value)}
                                placeholder="Enter image heading"
                                required
                            />
                            <InputError message={errors.heading} />
                        </div>
                        <div>
                            <Label htmlFor="edit-caption">Caption *</Label>
                            <Textarea
                                id="edit-caption"
                                value={formData.caption}
                                onChange={(e) => setData('caption', e.target.value)}
                                placeholder="Enter image caption"
                                rows={3}
                                required
                            />
                            <InputError message={errors.caption} />
                        </div>
                        <div>
                            <Label htmlFor="edit-order">Display Order *</Label>
                            <Input
                                id="edit-order"
                                type="number"
                                min="1"
                                value={formData.order}
                                onChange={(e) => setData('order', parseInt(e.target.value) || 1)}
                                required
                            />
                            <InputError message={errors.order} />
                        </div>
                        <div className="flex gap-2">
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Updating...' : 'Update'}
                            </Button>
                            <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                                Cancel
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>

            {/* View Dialog */}
            <Dialog open={!!viewingItem} onOpenChange={() => setViewingItem(null)}>
                <DialogContent className="sm:max-w-lg">
                    <DialogHeader>
                        <DialogTitle>View Image Slider</DialogTitle>
                    </DialogHeader>
                    {viewingItem && (
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Title</Label>
                                    <p className="text-sm font-medium">{viewingItem.title}</p>
                                </div>
                                <div>
                                    <Label>Display Order</Label>
                                    <p className="text-sm font-medium">#{viewingItem.order}</p>
                                </div>
                            </div>
                            <div>
                                <Label>Heading</Label>
                                <p className="text-sm">{viewingItem.heading}</p>
                            </div>
                            <div>
                                <Label>Caption</Label>
                                <p className="text-sm text-gray-600">{viewingItem.caption}</p>
                            </div>
                            <div>
                                <Label>Image Preview</Label>
                                <img src={viewingItem.image} alt={viewingItem.title} className="h-32 w-32 rounded object-cover" />
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </div>
    );
}
